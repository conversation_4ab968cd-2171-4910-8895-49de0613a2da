"""
队列清理器
"""
import asyncio
from typing import List, Dict, Any
import aiohttp
from urllib.parse import quote
from loguru import logger

from config import settings


class QueueCleaner:
    """RabbitMQ队列清理器"""
    
    def __init__(self):
        self.session = None
        self.base_url = f"http://{settings.rabbitmq_management_host}:{settings.rabbitmq_management_port}/api"
        self.auth = aiohttp.BasicAuth(
            settings.rabbitmq_management_username,
            settings.rabbitmq_management_password
        )
        self.timeout = aiohttp.ClientTimeout(total=30)
    
    async def start(self):
        """启动清理器"""
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        logger.info("Queue cleaner started")
    
    async def stop(self):
        """停止清理器"""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("Queue cleaner stopped")
    
    async def get_all_queues(self) -> List[Dict[str, Any]]:
        """获取所有队列信息"""
        if not self.session:
            logger.error("HTTP session not initialized")
            return []
        
        url = f"{self.base_url}/queues"
        
        try:
            async with self.session.get(url, auth=self.auth) as response:
                if response.status == 200:
                    queues = await response.json()
                    logger.debug(f"Retrieved {len(queues)} queues")
                    return queues
                else:
                    logger.error(f"Failed to get queues: status={response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error getting queues: {e}")
            return []
    
    async def purge_queue(self, queue_name: str) -> bool:
        """清空指定队列"""
        if not self.session:
            logger.error("HTTP session not initialized")
            return False
        
        # URL编码队列名称
        encoded_queue_name = quote(queue_name, safe='')
        url = f"{self.base_url}/queues/%2F/{encoded_queue_name}/contents"
        
        try:
            async with self.session.delete(url, auth=self.auth) as response:
                if response.status == 204:
                    logger.info(f"Successfully purged queue: {queue_name}")
                    return True
                else:
                    logger.error(f"Failed to purge queue {queue_name}: status={response.status}")
                    return False
        except Exception as e:
            logger.error(f"Error purging queue {queue_name}: {e}")
            return False
    
    async def cleanup_target_queues(self):
        """清理目标队列（以指定前缀开头的队列）"""
        try:
            # 获取所有队列
            all_queues = await self.get_all_queues()
            
            # 过滤出需要清理的队列
            target_queues = [
                queue for queue in all_queues
                if queue.get("name", "").startswith(settings.cleanup_queue_prefix)
            ]
            
            if not target_queues:
                logger.debug(f"No queues found with prefix: {settings.cleanup_queue_prefix}")
                return
            
            logger.info(f"Found {len(target_queues)} queues to clean with prefix: {settings.cleanup_queue_prefix}")
            
            # 清理队列
            for queue in target_queues:
                queue_name = queue.get("name")
                if queue_name:
                    await self.purge_queue(queue_name)
                    # 添加小延迟避免过于频繁的请求
                    await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"Error in cleanup_target_queues: {e}")
    
    async def start_periodic_cleanup(self):
        """启动定期清理任务"""
        logger.info(f"Starting periodic cleanup every {settings.cleanup_interval_minutes} minutes")
        
        while True:
            try:
                await self.cleanup_target_queues()
                # 等待下次清理
                await asyncio.sleep(settings.cleanup_interval_minutes * 60)
            except asyncio.CancelledError:
                logger.info("Periodic cleanup task cancelled")
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")
                # 出错后等待一段时间再重试
                await asyncio.sleep(60)
