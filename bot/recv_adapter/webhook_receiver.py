"""
Webhook接收器
"""
import asyncio
import json
from typing import Callable, Dict, Any, Union
from aiohttp import web, ClientSession
from loguru import logger

from config import settings
from models import (
    WebhookMessage, WebhookContactMessage, WebhookDeleteContactMessage, 
    WebhookOfflineMessage, MessageVersion
)


class WebhookReceiver:
    """Webhook消息接收器"""
    
    def __init__(self):
        self.app = None
        self.runner = None
        self.site = None
        self.message_callback = None
        
    async def start(self, message_callback: Callable):
        """启动webhook服务器"""
        self.message_callback = message_callback
        
        # 创建aiohttp应用
        self.app = web.Application()
        
        # 添加路由
        self.app.router.add_post(settings.webhook_path, self.handle_webhook)
        self.app.router.add_get('/health', self.health_check)
        
        # 启动服务器
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(
            self.runner, 
            settings.webhook_host, 
            settings.webhook_port
        )
        await self.site.start()
        
        logger.info(f"Webhook server started on {settings.webhook_host}:{settings.webhook_port}{settings.webhook_path}")
    
    async def stop(self):
        """停止webhook服务器"""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
        logger.info("Webhook server stopped")
    
    async def health_check(self, request):
        """健康检查接口"""
        return web.json_response({
            "status": "ok",
            "service": "wechat-bot-webhook-receiver",
            "version": "3.0"
        })
    
    async def handle_webhook(self, request):
        """处理webhook请求"""
        try:
            # 获取请求数据
            data = await request.json()
            
            logger.debug(f"Received webhook data: {json.dumps(data, ensure_ascii=False)}")
            
            # 解析消息类型
            type_name = data.get("TypeName", "")
            
            if type_name == "AddMsg":
                # 普通消息
                await self._handle_add_message(data)
            elif type_name == "ModContacts":
                # 联系人变更
                await self._handle_contact_change(data)
            elif type_name == "DelContacts":
                # 删除联系人
                await self._handle_delete_contact(data)
            elif type_name == "Offline":
                # 掉线通知
                await self._handle_offline(data)
            else:
                logger.warning(f"Unknown webhook message type: {type_name}")
            
            return web.json_response({"status": "success"})
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in webhook request: {e}")
            return web.json_response(
                {"status": "error", "message": "Invalid JSON"}, 
                status=400
            )
        except Exception as e:
            logger.error(f"Error handling webhook: {e}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return web.json_response(
                {"status": "error", "message": str(e)}, 
                status=500
            )
    
    async def _handle_add_message(self, data: Dict[str, Any]):
        """处理AddMsg类型的消息"""
        try:
            # 验证消息格式
            webhook_msg = WebhookMessage(**data)
            
            # 调用消息处理回调
            if self.message_callback:
                await self.message_callback(data, "webhook", MessageVersion.V3)
                
        except Exception as e:
            logger.error(f"Error processing AddMsg webhook: {e}")
            raise
    
    async def _handle_contact_change(self, data: Dict[str, Any]):
        """处理联系人变更消息"""
        try:
            # 验证消息格式
            contact_msg = WebhookContactMessage(**data)
            
            logger.info(f"Contact changed: {contact_msg.Data.UserName}")
            # 这里可以添加联系人变更的处理逻辑
            
        except Exception as e:
            logger.error(f"Error processing contact change webhook: {e}")
    
    async def _handle_delete_contact(self, data: Dict[str, Any]):
        """处理删除联系人消息"""
        try:
            # 验证消息格式
            delete_msg = WebhookDeleteContactMessage(**data)
            
            logger.info(f"Contact deleted: {delete_msg.Data.UserName}")
            # 这里可以添加删除联系人的处理逻辑
            
        except Exception as e:
            logger.error(f"Error processing delete contact webhook: {e}")
    
    async def _handle_offline(self, data: Dict[str, Any]):
        """处理掉线通知"""
        try:
            # 验证消息格式
            offline_msg = WebhookOfflineMessage(**data)
            
            logger.warning(f"WeChat account offline: {offline_msg.Wxid}")
            # 这里可以添加掉线处理逻辑
            
        except Exception as e:
            logger.error(f"Error processing offline webhook: {e}")


# 全局webhook接收器实例
webhook_receiver = WebhookReceiver()
