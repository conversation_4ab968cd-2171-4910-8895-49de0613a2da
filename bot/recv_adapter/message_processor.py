"""
消息处理器
"""
import re
from typing import Dict, Any, Optional
from loguru import logger

from models import StandardMessage, MessageVersion


class MessageProcessor:
    """消息处理器"""
    
    def normalize_message_v1(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本1消息 (wechatpadpro)"""
        return StandardMessage(
            msg_id=data.get("msg_id", 0),
            msg_type=data.get("msg_type", 0),
            timestamp=data.get("create_time", 0),
            is_self_message=data.get("is_self_message", False),
            wxid=data.get("account_wxid", ""),
            uuid=data.get("account_uuid", ""),
            from_user_name=data.get("from_user_name", ""),
            to_user_name=data.get("to_user_name", ""),
            content=data.get("content", ""),
            push_content=data.get("push_content", ""),
            msg_source=data.get("msg_source", ""),
            ver=1
        )
    
    def normalize_message_v1_5(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本1.5消息 (wx_msg)"""
        add_msgs = data.get("AddMsgs", [])
        if not add_msgs:
            raise ValueError("No AddMsgs found in message")
        
        first_msg = add_msgs[0]
        user_name = data.get("userName", "")
        
        return StandardMessage(
            msg_id=first_msg.get("msg_id", 0),
            msg_type=first_msg.get("msg_type", 0),
            timestamp=first_msg.get("create_time", 0),
            is_self_message=first_msg.get("from_user_name", {}).get("str", "") == user_name,
            wxid=user_name,
            uuid=data.get("UUID", ""),
            from_user_name=first_msg.get("from_user_name", {}).get("str", ""),
            to_user_name=first_msg.get("to_user_name", {}).get("str", ""),
            content=first_msg.get("content", {}).get("str", ""),
            push_content=first_msg.get("push_content", ""),
            msg_source=first_msg.get("msg_source", ""),
            ver=15
        )
    
    def normalize_message_v2(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本2消息 (wxapi)"""
        data_obj = data.get("Data", {})
        add_msgs = data_obj.get("AddMsgs", [])
        if not add_msgs:
            raise ValueError("No AddMsgs found in Data")
        
        first_msg = add_msgs[0]
        wxid = data_obj.get("Wxid", "")
        
        return StandardMessage(
            msg_id=first_msg.get("MsgId", 0),
            msg_type=first_msg.get("MsgType", 0),
            timestamp=first_msg.get("CreateTime", 0),
            is_self_message=first_msg.get("FromUserName", {}).get("string", "") == wxid,
            wxid=wxid,
            uuid="",  # 版本2没有UUID
            from_user_name=first_msg.get("FromUserName", {}).get("string", ""),
            to_user_name=first_msg.get("ToUserName", {}).get("string", ""),
            content=first_msg.get("Content", {}).get("string", ""),
            push_content=first_msg.get("PushContent", ""),
            msg_source=first_msg.get("MsgSource", ""),
            ver=2
        )
    
    def normalize_message(self, data: Dict[str, Any], queue_name: str, version: int) -> StandardMessage:
        """根据版本标准化消息"""
        try:
            if version == 1:
                if queue_name == "wx_msg":
                    return self.normalize_message_v1_5(data)
                else:
                    return self.normalize_message_v1(data)
            elif version == 2:
                return self.normalize_message_v2(data)
            elif version == 3:
                return self.normalize_message_v3(data)
            else:
                raise ValueError(f"Unsupported message version: {version}")
        except Exception as e:
            logger.error(f"Failed to normalize message from {queue_name} (v{version}): {e}")
            logger.debug(f"Raw data: {data}")
            raise

    def normalize_message_v3(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本3消息 (webhook)"""
        # 提取基本信息
        appid = data.get("Appid", "")
        wxid = data.get("Wxid", "")
        msg_data = data.get("Data", {})

        # 提取消息字段
        msg_id = msg_data.get("MsgId", 0)
        msg_type = msg_data.get("MsgType", 0)
        create_time = msg_data.get("CreateTime", 0)

        # 提取用户信息
        from_user = msg_data.get("FromUserName", {}).get("string", "")
        to_user = msg_data.get("ToUserName", {}).get("string", "")

        # 提取消息内容
        content = msg_data.get("Content", {}).get("string", "")
        push_content = msg_data.get("PushContent", "")
        msg_source = msg_data.get("MsgSource", "")

        # 判断是否是自己发送的消息
        is_self_message = from_user == wxid

        return StandardMessage(
            msg_id=msg_id,
            msg_type=msg_type,
            timestamp=create_time,
            is_self_message=is_self_message,
            wxid=wxid,
            uuid=appid,  # 使用Appid作为uuid
            from_user_name=from_user,
            to_user_name=to_user,
            content=content,
            push_content=push_content,
            msg_source=msg_source,
            ver=3
        )

