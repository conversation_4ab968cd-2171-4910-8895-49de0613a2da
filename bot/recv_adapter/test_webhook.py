#!/usr/bin/env python3
"""
测试webhook功能
"""
import asyncio
import json
import time
from typing import Dict, Any
import aiohttp

from config import settings


class WebhookTester:
    """Webhook测试器"""
    
    def __init__(self):
        self.base_url = f"http://{settings.webhook_host}:{settings.webhook_port}"
        self.webhook_url = f"{self.base_url}{settings.webhook_path}"
        self.health_url = f"{self.base_url}/health"
    
    async def test_health_check(self):
        """测试健康检查接口"""
        print("🔍 测试健康检查接口...")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(self.health_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 健康检查成功: {data}")
                        return True
                    else:
                        print(f"❌ 健康检查失败: HTTP {response.status}")
                        return False
            except Exception as e:
                print(f"❌ 健康检查异常: {e}")
                return False
    
    async def test_webhook_message(self):
        """测试webhook消息接收"""
        print("📨 测试webhook消息接收...")
        
        # 构造测试消息
        test_message = {
            "TypeName": "AddMsg",
            "Appid": "test_app_001",
            "Wxid": "test_wxid_123",
            "Data": {
                "MsgId": int(time.time() * 1000),  # 使用当前时间戳作为消息ID
                "FromUserName": {"string": "test_user_456"},
                "ToUserName": {"string": "test_wxid_123"},
                "MsgType": 1,  # 文本消息
                "Content": {"string": "这是一条webhook测试消息"},
                "Status": 3,
                "ImgStatus": 1,
                "ImgBuf": {},
                "CreateTime": int(time.time()),
                "MsgSource": "",
                "PushContent": "这是一条webhook测试消息",
                "NewMsgId": int(time.time() * 1000) + 1,
                "MsgSeq": 1
            }
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                headers = {'Content-Type': 'application/json'}
                async with session.post(
                    self.webhook_url, 
                    json=test_message, 
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 消息发送成功: {data}")
                        return True
                    else:
                        text = await response.text()
                        print(f"❌ 消息发送失败: HTTP {response.status}, {text}")
                        return False
            except Exception as e:
                print(f"❌ 消息发送异常: {e}")
                return False
    
    async def test_webhook_group_message(self):
        """测试webhook群组消息"""
        print("👥 测试webhook群组消息...")
        
        # 构造群组测试消息
        group_message = {
            "TypeName": "AddMsg",
            "Appid": "test_app_001",
            "Wxid": "test_wxid_123",
            "Data": {
                "MsgId": int(time.time() * 1000) + 100,
                "FromUserName": {"string": "test_group_789@chatroom"},
                "ToUserName": {"string": "test_wxid_123"},
                "MsgType": 1,
                "Content": {"string": "test_sender:这是一条群组测试消息 @test_user_456"},
                "Status": 3,
                "ImgStatus": 1,
                "ImgBuf": {},
                "CreateTime": int(time.time()),
                "MsgSource": "<atuserlist><![CDATA[test_user_456,test_user_789]]></atuserlist><membercount>50</membercount>",
                "PushContent": "这是一条群组测试消息",
                "NewMsgId": int(time.time() * 1000) + 101,
                "MsgSeq": 2
            }
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                headers = {'Content-Type': 'application/json'}
                async with session.post(
                    self.webhook_url, 
                    json=group_message, 
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 群组消息发送成功: {data}")
                        return True
                    else:
                        text = await response.text()
                        print(f"❌ 群组消息发送失败: HTTP {response.status}, {text}")
                        return False
            except Exception as e:
                print(f"❌ 群组消息发送异常: {e}")
                return False
    
    async def test_webhook_contact_change(self):
        """测试联系人变更消息"""
        print("👤 测试联系人变更消息...")
        
        contact_message = {
            "TypeName": "ModContacts",
            "Appid": "test_app_001",
            "Wxid": "test_wxid_123",
            "Data": {
                "UserName": {"string": "new_contact_123"},
                "NickName": {"string": "新联系人"},
                "PyInitial": {"string": "XLX"},
                "QuanPin": {"string": "xinlianxiren"},
                "Sex": 1,
                "ImgBuf": {},
                "BitMask": 0,
                "BitVal": 0,
                "ImgFlag": 0,
                "ContactType": 1
            }
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                headers = {'Content-Type': 'application/json'}
                async with session.post(
                    self.webhook_url, 
                    json=contact_message, 
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 联系人变更消息发送成功: {data}")
                        return True
                    else:
                        text = await response.text()
                        print(f"❌ 联系人变更消息发送失败: HTTP {response.status}, {text}")
                        return False
            except Exception as e:
                print(f"❌ 联系人变更消息发送异常: {e}")
                return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始webhook功能测试...")
        print(f"测试目标: {self.webhook_url}")
        print("-" * 50)
        
        results = []
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        await asyncio.sleep(2)
        
        # 测试健康检查
        results.append(await self.test_health_check())
        
        # 测试普通消息
        results.append(await self.test_webhook_message())
        
        # 测试群组消息
        results.append(await self.test_webhook_group_message())
        
        # 测试联系人变更
        results.append(await self.test_webhook_contact_change())
        
        # 统计结果
        passed = sum(results)
        total = len(results)
        
        print("-" * 50)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
        else:
            print("⚠️ 部分测试失败，请检查日志")
        
        return passed == total


async def main():
    """主函数"""
    tester = WebhookTester()
    success = await tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
